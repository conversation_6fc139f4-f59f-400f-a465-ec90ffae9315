'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis } from 'recharts';
import { AssessmentScores } from '../../types/assessment-results';
import { BarChart3 } from 'lucide-react';

interface RiasecRadarChartProps {
  scores: AssessmentScores;
}

export default function RiasecRadarChart({ scores }: RiasecRadarChartProps) {
  // Transform RIASEC scores for radar chart
  const radarData = [
    {
      category: 'R',
      fullName: 'Realistic',
      description: '<PERSON><PERSON><PERSON>, hands-on, teknis',
      score: scores.riasec.realistic,
      fullMark: 100,
    },
    {
      category: 'I',
      fullName: 'Investigative',
      description: 'Analitis, penelitian, ilmiah',
      score: scores.riasec.investigative,
      fullMark: 100,
    },
    {
      category: 'A',
      fullName: 'Artistic',
      description: 'Kreatif, ekspresif, inovatif',
      score: scores.riasec.artistic,
      fullMark: 100,
    },
    {
      category: 'S',
      fullName: 'Social',
      description: 'Membantu, mengajar, melayani',
      score: scores.riasec.social,
      fullMark: 100,
    },
    {
      category: 'E',
      fullName: 'Enterprising',
      description: 'Memimpin, menjual, persuasif',
      score: scores.riasec.enterprising,
      fullMark: 100,
    },
    {
      category: 'C',
      fullName: 'Conventional',
      description: 'Terorganisir, detail, sistematis',
      score: scores.riasec.conventional,
      fullMark: 100,
    },
  ];

  const chartConfig = {
    score: {
      label: "Score",
      color: "#10b981",
    },
  };

  // Calculate statistics
  const averageScore = Math.round(radarData.reduce((sum, item) => sum + item.score, 0) / radarData.length);
  const highestScore = Math.max(...radarData.map(item => item.score));
  const strongestType = radarData.find(item => item.score === highestScore);

  // Get top 3 Holland codes
  const topThree = radarData
    .sort((a, b) => b.score - a.score)
    .slice(0, 3)
    .map(item => item.category)
    .join('');

  return (
    <Card className="bg-white border-gray-200/60 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-[#10b981]/10 rounded-lg">
            <BarChart3 className="w-5 h-5 text-[#10b981]" />
          </div>
          <div>
            <CardTitle className="text-lg font-semibold text-[#1f2937]">
              RIASEC Holland Codes Radar
            </CardTitle>
            <p className="text-xs text-[#6b7280]">Visualisasi tipe kepribadian karir</p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[400px]">
          <RadarChart data={radarData} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent
                hideLabel
                formatter={(value, name) => {
                  const item = radarData.find(item => item.category === name);
                  return [
                    `${value}%`,
                    item ? `${item.fullName} - ${item.description}` : name
                  ];
                }}
              />}
            />
            <PolarGrid
              stroke="#e2e8f0"
              strokeWidth={1}
              strokeOpacity={0.4}
              fill="none"
              gridType="polygon"
            />
            <PolarAngleAxis
              dataKey="category"
              tick={{
                fill: '#374151',
                fontSize: 12,
                fontWeight: 600
              }}
              tickFormatter={(value) => value}
              className="text-[#374151]"
            />
            <PolarRadiusAxis
              angle={90}
              domain={[0, 100]}
              tick={{
                fill: '#9ca3af',
                fontSize: 10
              }}
              tickCount={6}
              axisLine={false}
              className="text-[#9ca3af]"
            />
            <Radar
              dataKey="score"
              stroke="#10b981"
              fill="#10b981"
              fillOpacity={0.15}
              strokeWidth={2.5}
              dot={{
                fill: '#10b981',
                strokeWidth: 2,
                stroke: '#ffffff',
                r: 4,
              }}
              activeDot={{
                fill: '#10b981',
                strokeWidth: 3,
                stroke: '#ffffff',
                r: 6,
              }}
            />
          </RadarChart>
        </ChartContainer>
        
        {/* Legend/Summary */}
        <div className="mt-4 space-y-4">
          {/* Holland Code Summary */}
          <div className="p-3 bg-gradient-to-r from-[#10b981]/5 to-[#059669]/5 rounded-lg border border-[#10b981]/10">
            <div className="text-center">
              <div className="text-2xl font-bold text-[#10b981] mb-1">
                {topThree}
              </div>
              <div className="text-xs text-[#6b7280] font-medium">Holland Code Anda</div>
              <div className="text-xs text-[#6b7280] mt-1">
                Kombinasi 3 tipe kepribadian karir tertinggi
              </div>
            </div>
          </div>

          {/* Type Details - 3x2 Grid Layout */}
          <div className="grid grid-cols-3 gap-2 text-xs">
            {radarData.map((item, index) => (
              <div key={index} className="flex flex-col items-center justify-between p-2 bg-gray-50/50 rounded-lg border border-gray-100 min-h-[80px]">
                <div className="flex flex-col items-center gap-1 text-center">
                  <div className="w-6 h-6 rounded-full bg-[#10b981]/10 flex items-center justify-center">
                    <span className="font-bold text-[#10b981] text-xs">{item.category}</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <span className="font-semibold text-[#374151] text-xs leading-tight">{item.fullName}</span>
                    <span className="text-[#6b7280] text-xs leading-tight">{item.description}</span>
                  </div>
                </div>
                <div className="text-center mt-1">
                  <span className="text-[#10b981] font-bold text-sm">{item.score}</span>
                  <span className="text-[#9ca3af] text-xs ml-0.5">%</span>
                </div>
              </div>
            ))}
          </div>

          {/* Overall Statistics */}
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-[#10b981]/5 to-[#059669]/5 rounded-lg border border-[#10b981]/10">
            <div className="text-center">
              <div className="text-xl font-bold text-[#10b981]">
                {averageScore}%
              </div>
              <div className="text-xs text-[#6b7280] font-medium">Rata-rata</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-[#059669]">
                {highestScore}%
              </div>
              <div className="text-xs text-[#6b7280] font-medium">Tertinggi</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-bold text-[#dc2626] max-w-[80px] truncate">
                {strongestType?.fullName}
              </div>
              <div className="text-xs text-[#6b7280] font-medium">Terkuat</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
